# 对象参数变成"[object Object]"问题的最终解决方案

## 问题描述
第一次跳转到科室选择页面再返回来是好的，但第二次跳转返回后 `bookArr` 和 `pointRelease` 就变成 `"[object Object]"` 字符串了。

## 问题根源分析

### 对象参数在URL传递中的问题
1. **原始状态**：`pointRelease` 和 `bookArr` 在原始页面中是对象
2. **URL传递限制**：Vue Router 在传递对象参数时会自动调用 `toString()` 方法
3. **对象转字符串**：对象的 `toString()` 方法返回 `"[object Object]"`

### 具体流程问题
```javascript
// 原始参数（对象）
{
  pointRelease: { taskId: 123, taskPointName: "测试点" },
  bookArr: [{ id: 1, name: "项目1" }, { id: 2, name: "项目2" }]
}

// 第一次跳转：直接传递对象
// Vue Router 自动转换为字符串
{
  pointRelease: "[object Object]",
  bookArr: "[object Object]"
}

// 第二次跳转：已经是字符串，继续传递
{
  pointRelease: "[object Object]",
  bookArr: "[object Object]"
}
```

## 最终解决方案

### 智能序列化/反序列化机制

#### 修改 feedback.vue 的 toSelectDept 方法
```javascript
toSelectDept() {
  // 排除selectedOffice参数，避免重复传递
  const { selectedOffice, ...cleanQuery } = this.$route.query;

  // 处理对象参数，确保正确序列化
  const processedQuery = {};
  Object.keys(cleanQuery).forEach(key => {
    const value = cleanQuery[key];
    if (typeof value === 'object' && value !== null) {
      // 对象参数需要序列化
      processedQuery[key] = JSON.stringify(value);
    } else {
      // 字符串参数直接传递
      processedQuery[key] = value;
    }
  });

  this.$router.push({
    path: "/deptSelectedPage",
    query: {
      fromPath: this.$route.path,
      // 标记哪些参数是序列化的对象
      _serializedParams: JSON.stringify(Object.keys(cleanQuery).filter(key =>
        typeof cleanQuery[key] === 'object' && cleanQuery[key] !== null
      )),
      ...processedQuery
    }
  });
}
```

#### 修改 deptSelectedPage.vue 的 go 方法
```javascript
go(type) {
  // 获取当前路由的所有参数，排除科室选择页面特有的参数
  const { fromPath, _serializedParams, ...originalQuery } = this.$route.query;

  // 反序列化对象参数
  const processedQuery = {};
  let serializedParamNames = [];

  try {
    if (_serializedParams) {
      serializedParamNames = JSON.parse(_serializedParams);
    }
  } catch (error) {
    console.error('解析序列化参数名称失败:', error);
  }

  Object.keys(originalQuery).forEach(key => {
    const value = originalQuery[key];
    if (serializedParamNames.includes(key)) {
      // 反序列化对象参数
      try {
        processedQuery[key] = JSON.parse(value);
      } catch (error) {
        console.error(`反序列化参数 ${key} 失败:`, error);
        processedQuery[key] = value; // 失败时使用原值
      }
    } else {
      // 字符串参数直接使用
      processedQuery[key] = value;
    }
  });

  this.$router.replace({
    path: fromPath || '/repair',
    query: {
      ...processedQuery, // 恢复所有原有参数
      selectedOffice: this.selectedName // 添加选择的科室信息
    },
  });
  this.$router.go(-1);
}
```

## 解决方案特点

### 智能处理机制
1. **自动识别**：自动识别哪些参数是对象，哪些是字符串
2. **标记机制**：使用 `_serializedParams` 标记哪些参数被序列化了
3. **安全反序列化**：带错误处理的反序列化，失败时使用原值
4. **类型保持**：确保对象参数恢复为对象，字符串参数保持字符串

### 参数传递流程
1. **跳转时**：
   - 检测对象参数并序列化
   - 记录哪些参数被序列化
   - 字符串参数直接传递
2. **返回时**：
   - 根据标记反序列化对象参数
   - 字符串参数直接使用
   - 添加新的科室信息

## 预期效果
- ✅ 解决 pointRelease 和 bookArr 变成 "[object Object]" 的问题
- ✅ 支持多次跳转而不出错
- ✅ 保持所有参数的正确数据类型
- ✅ 带错误处理，提高稳定性
- ✅ 自动识别参数类型，无需手动配置
