<template>
  <div class="department">
    <Header title="选择科室" @backFun="goback"></Header>
    <div class="department-search">
      <div class="search-ipt" v-show="!query">
        <span class="iconfont">&#xe60a;</span>
        <input v-model="officeName" class="search" type="text" maxlength="50" placeholder="请输入关键字搜索" />
        <span class="line"></span>
        <span class="cancle" @click="switchto">取消</span>
      </div>

      <div class="info" v-if="skip == 2">
        <div class="st" @click="switchto" v-show="query">
          <span class="iconfont">&#xe60a;</span>
          <span class="search-text">请输入关键字搜索</span>
        </div>
        <ul class="content-list" v-if="query">
          <li class="item" v-for="(item, index) of officeNameList" :key="index" @click="goToRevise(item, 3)">
            {{ item.officeName }}
          </li>
        </ul>
        <ul class="content-list" v-else>
          <li class="item" v-for="(item, index) of setofficeNameList" :key="index" @click="goToRevise(item, 3)">
            {{ item.officeName }}
          </li>
        </ul>
      </div>
      <div class="info" v-if="skip == 1">
        <div class="search-tap st" v-show="query">
          <span class="tapone" @click="switchto(1)">
            <span class="iconfont">&#xe60a;</span>
            <span class="search-text">请输入关键字搜索</span>
          </span>
          <!-- <span class="taptwo" @click="switchto(2)">
            <span class="borleft"></span>
            <span class="text">科室不对</span>
          </span> -->
        </div>
        <ul class="content-list" v-show="query">
          <li class="item" v-for="(item, index) of officeNameList" :key="index" @click="goToRevise(item, 3)">
            {{ item.name }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import { mapState } from "vuex";
export default {
  name: "deptName",
  data() {
    return {
      skip: 1,
      query: true,
      officeName: "", //查询科室
      officeNameList: [], //科室列表
      setofficeNameList: [], //搜索科室列表
      selectedName: {}, //选中的科室
      areaVal: [],
      type: "",
      serviceid: "",
      level: "", //为2时是运送的终点
    };
  },
  computed: {
    ...mapState(["loginInfo", "staffInfo"]),
  },
  methods: {
    // 公共的参数恢复方法
    restoreParams(addSelectedOffice = false) {
      console.log('恢复参数，addSelectedOffice:', addSelectedOffice);

      // 获取当前路由的所有参数，排除科室选择页面特有的参数
      const { fromPath, _serializedParams, ...originalQuery } = this.$route.query;

      console.log('返回时恢复参数:', originalQuery);
      console.log('_serializedParams:', _serializedParams);

      // 反序列化对象参数
      const processedQuery = {};
      let serializedParamNames = [];

      try {
        if (_serializedParams) {
          serializedParamNames = JSON.parse(_serializedParams);
          console.log('需要反序列化的参数:', serializedParamNames);
        }
      } catch (error) {
        console.error('解析序列化参数名称失败:', error);
      }

      Object.keys(originalQuery).forEach(key => {
        const value = originalQuery[key];
        if (serializedParamNames.includes(key)) {
          // 反序列化对象参数
          console.log(`反序列化参数 ${key}:`, value);
          try {
            const parsed = JSON.parse(value);
            processedQuery[key] = parsed;
            console.log(`反序列化成功 ${key}:`, parsed);
          } catch (error) {
            console.error(`反序列化参数 ${key} 失败:`, error);
            processedQuery[key] = value; // 失败时使用原值
          }
        } else {
          // 字符串参数直接使用
          console.log(`直接使用参数 ${key}:`, value);
          processedQuery[key] = value;
        }
      });

      // 如果需要添加选择的科室信息
      if (addSelectedOffice && this.selectedName) {
        processedQuery.selectedOffice = this.selectedName;
      }

      console.log('最终返回的参数:', processedQuery);

      return {
        path: fromPath || '/repair',
        query: processedQuery
      };
    },

    goback() {
      console.log('用户点击返回按钮，不选择科室');

      // 检查是否来自 feedback 页面，只对这种情况使用参数恢复逻辑
      const fromPath = this.$route.query.fromPath;
      const isFromFeedback = fromPath && fromPath.includes('feedback');
      const hasSerializedParams = !!this.$route.query._serializedParams;

      console.log('fromPath:', fromPath);
      console.log('isFromFeedback:', isFromFeedback);
      console.log('hasSerializedParams:', hasSerializedParams);
      console.log('_serializedParams:', this.$route.query._serializedParams);

      if (isFromFeedback && hasSerializedParams) {
        console.log('✅ 来自feedback页面且有序列化参数，使用参数恢复逻辑');
        const routeConfig = this.restoreParams(false); // 不添加selectedOffice
        this.$router.replace(routeConfig);
      } else {
        console.log('❌ 来自其他页面或无序列化参数，使用默认返回逻辑');
        console.log('原因: isFromFeedback =', isFromFeedback, ', hasSerializedParams =', hasSerializedParams);
        this.$router.go(-1);
      }
    },
    switchto(i) {
      if (i == 2) {
        this.skip = 2;
        this.getDeptName(3);
      } else {
        this.query = !this.query;
        this.officeName = "";
      }
    },
    getDeptName(type, officeId, localtionId, flag) {
      // this.axios
      //   .post(
      //     __PATH.ONESTOP +
      //       "/appOlgTaskManagement/getNameOffice",
      //     this.$qs.stringify({
      //       unitCode: this.loginInfo.userOffice[0].unitCode,
      //       hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
      //       type:type,
      //       officeId:officeId,  //type为1时必填  科室id
      //       localtionId:localtionId  //type为2的时候必填 地点id
      //     })
     this.$api.getAllOffice({})
        .then((res) => {
          console.log("getAllOffice", res);
          res = res.body.result;
          res.forEach((item) => {
            item.name = item.officeName;
          });
          // if(type==3){
          this.officeNameList = res;
          // }else if(type==1){
          //   this.areaVal=res;
          // }else if(type==2){
          //   this.officeNameList=res;
          if (res.length == 0) {
            this.switchto(2);
          }
          // }
          if (flag) {
            this.go(1);
          }
        });
    },
    goToRevise(item, type) {
      console.log("item", item);
      if (type == 1) {
        this.selectedName = item;
        this.getDeptName(1, item.id, "", true);
      } else if (type == 3) {
        this.selectedName = item;
        this.go(3);
      } else if (type == 2) {
        if (item.length > 0) {
          for (let i = 0; i < item.length; i++) {
            // 筛选符合条件的地点  gridLevel>2
            if (item[i].gridLevel <= 2) {
              item.splice(i, 1);
            }
          }
        }
        this.selectedName = item;
        this.go(2);
      }
    },
    /**
     * 返回上级页面并带回数据
     */
    go(type) {
      console.log('用户选择科室并返回');

      // 检查是否来自 feedback 页面，只对这种情况使用参数恢复逻辑
      const fromPath = this.$route.query.fromPath;
      const isFromFeedback = fromPath && fromPath.includes('feedback');
      const hasSerializedParams = !!this.$route.query._serializedParams;

      console.log('fromPath:', fromPath);
      console.log('isFromFeedback:', isFromFeedback);
      console.log('hasSerializedParams:', hasSerializedParams);
      console.log('_serializedParams:', this.$route.query._serializedParams);

      if (isFromFeedback && hasSerializedParams) {
        console.log('✅ 来自feedback页面且有序列化参数，使用参数恢复逻辑');
        const routeConfig = this.restoreParams(true); // 添加selectedOffice
        this.$router.replace(routeConfig);
        this.$router.go(-1);
      } else {
        console.log('❌ 来自其他页面或无序列化参数，使用原有逻辑');
        console.log('原因: isFromFeedback =', isFromFeedback, ', hasSerializedParams =', hasSerializedParams);
        // 原有的逻辑
        this.$router.replace({
          path: this.$route.query.fromPath || '/repair',
          query: {
            selectedOffice: this.selectedName,
            isRecipient: this.$route.query.isRecipient
          },
        });
        this.$router.go(-1);
      }
    },
  },
  mounted() {
    console.log(this.$route.query, "dept-----this.$route.query");
    this.$YBS.apiCloudEventKeyBack(this.goback);
    this.type = this.$route.query.type;
    this.level = this.$route.query.level;
    // if(this.$route.query.type==1 ){
    //   // type= 1 展示所有科室,带出关联地点
    //   this.skip=1
    //   this.getDeptName(3)
    // }else if(this.$route.query.type==3){
    //   // type= 3 展示所有科室
    //   this.skip=2
    this.getDeptName(3);
    // }else if(this.$route.query.type==2){
    //   //type= 2  展示相关科室,不带出地点
    //   this.serviceid=this.$route.query.serviceSiteResId[0]
    //   this.getDeptName(2,"",this.serviceid)
    // }
  },
  components: {},
  watch: {
    officeName() {
      if (!this.officeName) {
        this.setofficeNameList = [];
        this.query = false;
        return;
      }
      this.skip = 2;
      // this.axios
      //   .post(
      //     __PATH.ONESTOP +
      //       "/appOlgTaskManagement/getNameOffice",
      //     this.$qs.stringify({
      //       unitCode: this.loginInfo.userOffice[0].unitCode,
      //       hospitalCode: this.loginInfo.userOffice[0].hospitalCode,
      //       type:3,
      //       officeName:this.officeName
      //     })
      this.$api.getAllOffice({
        current: 1,
        size: 999,
        deptName: this.officeName,
      }).then((res) => {
        res = res.body.result;
        res.forEach((item) => {
          item.name = item.officeName;
        });
        this.setofficeNameList = res;
      });
    },
  },
};
</script>
};
</script>

<style rel="stylesheet/stylus" lang="stylus" scoped type="stylesheet/stylus">
@import '~styles/varibles.styl';
@import '~styles/mixins.styl';

.department
  height :100vh;
  overflow : hidden
.info
  height :100vh;
  overflow : hidden
.content-list
  height :calc(100% - 2.6rem);
  overflow-y:auto;
  .item
    margin-bottom: 1px
    padding: 0px .3rem
    font-size: .30rem
    border-bottom:1px solid #EFEFF4;
    background :#fff;
    padding:0.28rem 0.3rem;
    line-height :0.42rem;
    word-wrap:break-word;
    word-break:break-all;
.department-search
  width: 100%;
  height: 0.78rem;
  line-height: 0.78rem;
  padding: 0.2rem 0rem;
  background-color: #fff;
  border-bottom:1px solid #EFEFF4;
  .search-ipt
    position: relative;
    .iconfont
      position: absolute;
      left: 0.4rem;
      top: 0rem;
      color: $textColor;
.search
  background-color: $bgColor;
  padding-left: 0.8rem;
  padding-right: 1.1rem;
  box-sizing: border-box;
  height: 0.78rem;
  display: inline-block;
  width: 95%;
  margin-left: 2.5%;
  border-radius: 5px;
.line
  width: 1px;
  height: 0.3rem;
  background-color: #D3D3D5;
  display: inline-block;
  position: absolute;
  right: 1.1rem;
  top: 0.24rem;
.cancle
  position: absolute;
  right: 7px;
  top: 0;
  width: 1rem;
  text-align: center;

.st
  width: 95%;
  height: 0.78rem;
  margin-left: 2.5%;
  box-sizing: border-box;
  padding-left: 0.28rem;
  padding-right: .28rem;
  border-radius: 5px;
  color:$textColor;
  font-size:14px;
  background:#EFEFF4;
.search-tap
  display:flex;
  justify-content :space-between;
  .taptwo
    .borleft
      display: inline-block;
      width: 1px;
      height: 0.3rem;
      background-color: #D3D3D5;
      margin-top:.2rem;
      // position: absolute;
      // right: 1.1rem;
      // top: 0.24rem;
    .text
      color:#38C7C4;
      font-family:PingFang SC;
      font-style:italic;
.item-color{
  background : #fcfcfc ;
}
.search-text{
  font-size:.28rem
}
</style>
