# 第二次跳转报错问题分析与解决

## 问题描述
第一次跳转到科室选择页面再返回来，确实可以正常工作，但是第二次跳转就报错了。

## 问题分析

### 第一次跳转流程
1. **初始状态**：`this.$route.query` 包含原始参数
   ```javascript
   {
     pointRelease: "...",
     answerMapList: "...",
     entryTime: "...",
     // 其他原始参数
   }
   ```

2. **跳转时**：序列化所有参数
   ```javascript
   originalQuery: JSON.stringify(this.$route.query)
   ```

3. **返回时**：恢复参数并添加selectedOffice
   ```javascript
   {
     pointRelease: "...",
     answerMapList: "...",
     entryTime: "...",
     selectedOffice: { id: "123", officeName: "内科" }
   }
   ```

### 第二次跳转流程（问题所在）
1. **当前状态**：`this.$route.query` 现在包含selectedOffice
   ```javascript
   {
     pointRelease: "...",
     answerMapList: "...",
     entryTime: "...",
     selectedOffice: { id: "123", officeName: "内科" }  // 第一次选择的科室
   }
   ```

2. **跳转时**：序列化包含selectedOffice的参数
   ```javascript
   originalQuery: JSON.stringify(this.$route.query)  // 包含了selectedOffice
   ```

3. **返回时**：恢复参数（包含旧的selectedOffice）并添加新的selectedOffice
   ```javascript
   {
     ...originalQuery,  // 包含旧的selectedOffice
     selectedOffice: this.selectedName  // 新的selectedOffice
   }
   ```

### 问题根源
- selectedOffice参数被重复传递和覆盖
- 可能导致数据结构冲突或JSON解析错误
- 第二次跳转时originalQuery中包含了不应该保存的selectedOffice参数

## 解决方案

### 修改toSelectDept方法
在序列化参数时排除selectedOffice参数：

```javascript
toSelectDept() {
  // 排除selectedOffice参数，避免重复传递
  const { selectedOffice, ...cleanQuery } = this.$route.query;
  
  this.$router.push({
    path: "/deptSelectedPage",
    query: {
      fromPath: this.$route.path,
      // 保存当前页面的所有参数（排除selectedOffice），以便返回时恢复
      originalQuery: JSON.stringify(cleanQuery)
    }
  });
}
```

### 修改后的流程
1. **第二次跳转时**：只序列化原始业务参数，排除selectedOffice
2. **返回时**：恢复干净的原始参数，添加新选择的科室
3. **避免冲突**：不会出现selectedOffice参数重复或冲突的问题

## 测试场景
1. 进入feedback页面
2. 第一次选择科室 → 成功
3. 第二次选择科室 → 应该也成功，不再报错
4. 多次选择科室 → 都应该正常工作

## 预期效果
- 解决第二次跳转报错的问题
- 支持多次选择科室而不出错
- 保持参数传递的稳定性和可靠性
