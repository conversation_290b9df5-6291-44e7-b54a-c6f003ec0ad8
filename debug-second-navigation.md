# bookArr参数变成"[object Object]"问题分析与解决

## 问题描述
第一次跳转到科室选择页面再返回来可以正常工作，但是第二次跳转就报错了。经过分析发现，`bookArr` 这个参数的值变成了很多个 `"[object Object]"` 组成的数组。

## 问题根源分析

### JSON序列化问题
1. **原始参数**：URL参数中的 `answerMapList`、`bookArr` 等本身就是JSON字符串
2. **双重序列化**：使用 `JSON.stringify(this.$route.query)` 会对已经是字符串的JSON再次序列化
3. **对象转字符串**：复杂对象在URL传递过程中被错误转换为 `"[object Object]"`

### 具体流程问题
```javascript
// 原始参数（已经是JSON字符串）
{
  answerMapList: '{"key": "value"}',
  bookArr: '[{"id": 1}, {"id": 2}]'
}

// 第一次JSON.stringify后
originalQuery: '{"answerMapList": "{\\"key\\": \\"value\\"}","bookArr": "[{\\"id\\": 1}, {\\"id\\": 2}]"}'

// JSON.parse后再传递，对象变成字符串
{
  answerMapList: "[object Object]",
  bookArr: "[object Object]"
}
```

## 解决方案

### 避免JSON序列化，直接传递参数

#### 修改 feedback.vue 的 toSelectDept 方法
```javascript
toSelectDept() {
  // 排除selectedOffice参数，避免重复传递
  const { selectedOffice, ...cleanQuery } = this.$route.query;

  this.$router.push({
    path: "/deptSelectedPage",
    query: {
      fromPath: this.$route.path,
      // 直接传递所有参数，避免JSON序列化问题
      ...cleanQuery
    }
  });
}
```

#### 修改 deptSelectedPage.vue 的 go 方法
```javascript
go(type) {
  // 获取当前路由的所有参数，排除科室选择页面特有的参数
  const { fromPath, ...originalQuery } = this.$route.query;

  this.$router.replace({
    path: fromPath || '/repair',
    query: {
      ...originalQuery, // 恢复所有原有参数
      selectedOffice: this.selectedName // 添加选择的科室信息
    },
  });
  this.$router.go(-1);
}
```

## 修复效果

### 优势
1. **避免序列化问题**：不再使用JSON.stringify/parse，避免双重序列化
2. **保持数据类型**：参数保持原始的数据类型和结构
3. **简化逻辑**：代码更简洁，减少出错可能性
4. **性能提升**：避免不必要的序列化/反序列化操作

### 参数传递流程
1. **跳转时**：直接展开原始参数（排除selectedOffice）
2. **返回时**：直接展开所有参数，添加新的selectedOffice
3. **结果**：所有参数保持原始格式，不会变成"[object Object]"

## 测试场景
1. 进入feedback页面，检查原始参数格式
2. 第一次选择科室，检查参数是否保持原始格式
3. 第二次选择科室，确认不再报错且参数正确
4. 多次选择科室，验证稳定性

## 预期效果
- ✅ 解决bookArr变成"[object Object]"的问题
- ✅ 解决第二次跳转报错的问题
- ✅ 保持所有参数的原始数据类型和结构
- ✅ 支持多次选择科室而不出错
