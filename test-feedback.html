<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理反馈页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        h1, h2 {
            color: #333;
        }
        ul {
            margin: 10px 0;
        }
        li {
            margin: 5px 0;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>设备管理反馈页面 - 所属科室功能测试</h1>
    
    <div class="test-section success">
        <h2>✅ 功能实现完成</h2>
        <p>已成功在设备管理反馈页面（<code>src/pages/equipmentManagement/feedback.vue</code>）中添加了所属科室字段功能。</p>
    </div>

    <div class="test-section info">
        <h2>📋 实现的功能</h2>
        <ul>
            <li><strong>UI界面</strong>：在服务地点下方添加了"所属科室"字段，标记为必填项（红色*号）</li>
            <li><strong>选择方式</strong>：支持两种选择方式：
                <ul>
                    <li>点击字段区域：弹出科室选择器（带搜索功能）</li>
                    <li>点击搜索图标：跳转到科室选择页面（<code>/deptSelectedPage</code>）</li>
                </ul>
            </li>
            <li><strong>搜索功能</strong>：在弹窗选择器中支持关键字检索科室</li>
            <li><strong>数据回显</strong>：从科室选择页面返回后自动回显选择的科室</li>
            <li><strong>表单验证</strong>：提交时验证科室是否已选择</li>
            <li><strong>数据提交</strong>：将选择的科室信息包含在提交参数中</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>🔧 技术实现细节</h2>
        <ul>
            <li><strong>数据字段</strong>：
                <ul>
                    <li><code>sourcesDeptName</code>：科室名称（用于显示）</li>
                    <li><code>sourcesDept</code>：科室ID（用于提交）</li>
                    <li><code>showDeptPickerFlag</code>：控制弹窗显示</li>
                    <li><code>deptList</code>：科室列表数据</li>
                    <li><code>deptSearchValue</code>：搜索关键词</li>
                </ul>
            </li>
            <li><strong>API接口</strong>：使用 <code>getAllOffice()</code> 获取科室列表</li>
            <li><strong>路由跳转</strong>：跳转到 <code>/deptSelectedPage</code> 进行科室选择</li>
            <li><strong>数据处理</strong>：格式化科室数据，确保有 <code>name</code> 和 <code>id</code> 字段</li>
        </ul>
    </div>

    <div class="test-section warning">
        <h2>⚠️ 注意事项</h2>
        <ul>
            <li>参考了 <code>cleaning.vue</code> 的实现方式，保持了一致的用户体验</li>
            <li>科室选择为必填项，提交时会进行验证</li>
            <li>支持从科室选择页面返回时的数据回显</li>
            <li>弹窗选择器支持实时搜索过滤</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>🎯 用户操作流程</h2>
        <ol>
            <li>用户进入巡检报修/自修页面</li>
            <li>填写任务点、服务地点等基本信息</li>
            <li>点击"所属科室"字段：
                <ul>
                    <li>方式1：点击字段区域 → 弹出科室选择器 → 可搜索 → 选择科室</li>
                    <li>方式2：点击搜索图标 → 跳转科室选择页面 → 搜索选择 → 返回页面</li>
                </ul>
            </li>
            <li>科室信息自动回显在页面上</li>
            <li>继续填写其他信息并提交</li>
        </ol>
    </div>

    <div class="test-section success">
        <h2>✨ 功能特点</h2>
        <ul>
            <li><strong>用户友好</strong>：提供两种选择方式，满足不同用户习惯</li>
            <li><strong>搜索便捷</strong>：支持关键字快速检索科室</li>
            <li><strong>数据准确</strong>：必填验证确保数据完整性</li>
            <li><strong>体验一致</strong>：与现有cleaning.vue保持一致的交互方式</li>
        </ul>
    </div>
</body>
</html>
