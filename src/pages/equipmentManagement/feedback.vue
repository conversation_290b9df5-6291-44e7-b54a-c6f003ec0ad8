<template>
  <div class='inner' :style="{paddingBottom: dynamicPadding}">
    <Header :title="$route.query.isRepairSelf == 1 ? '巡检自修' : '巡检报修'" @backFun="goBack"></Header>
    <div class="contenTop">
      <div class="topItem">
        <div class="itemTitle">任务点</div>
        <div class="itemConten">{{ pointInfo.taskPointName }}</div>
      </div>
      <div class="topItem" style="padding: 10px 0;">
        <div class="itemTitle">服务地点<span v-if="needSelectLocation" style="color: #ee0a24;">*</span></div>
        <div class="itemConten" style="line-height: 1.2;" @click="showLocationPicker = true" v-if="needSelectLocation">
          <span v-if="locationFullPath">{{ locationFullPath }}</span>
          <span v-else style="color: #c8c9cc;">请选择服务地点</span>
          <van-icon name="arrow" style="margin-left: 5px;" />
        </div>
        <div class="itemConten" style="line-height: 1.2;" v-else>{{ locationName }}</div>
      </div>
      <div class="topItem" style="padding: 10px 0;" v-if="inspectionPointType !=2">
        <div class="itemTitle">关联设备</div>
        <div>
          <van-radio-group v-model="radioCheck" direction="horizontal">
            <van-radio name="0" checked-color="#3562DB" icon-size="16px">是</van-radio>
            <van-radio name="1" checked-color="#3562DB" icon-size="16px">否</van-radio>
          </van-radio-group>
        </div>
      </div>
      <div v-if="flagShow && inspectionPointType !=2">

        <div class="topItem" style="padding: 10px 0;">
          <div class="itemConten" style="line-height: 1.2;" @click="clickEquipment" >
            <div class="itemTitle">设备名称<span  style="color: #ee0a24;">*</span></div>
            <span v-if="sourceDeptName">{{ sourceDeptName }}</span>
            <span v-else  style="color: #c8c9cc;">请选择设备</span>
            <van-icon name="arrow" style="margin-left: 5px;" />
          </div>
        </div>
        <div class="topItem" style="padding: 10px 0;">
          <div class="itemTitle">设备编码</div>
          <div class="itemConten" style="line-height: 1.2;">{{ equipmentCode }}</div>
        </div>
      </div>
      <van-popup v-model="showPicker" round position="bottom">
        <van-search placeholder="请输入" v-model="searchVal" />
        <van-picker
          show-toolbar
          :columns="searchOptions"
          @cancel="showPicker = false"
          @confirm="onConfirm"
          value-key="displayText">
          <template #option="{ assetName, assetCode }">
            <div style="display: flex; justify-content: space-between;width: 100%;padding: 0 10vw">
              <span style="width: 50%;">{{ assetName }}</span>
              <span style="width: 50%; text-align: right; color: #969799;">{{ assetCode }}</span>
            </div>
          </template>
        </van-picker>
      </van-popup>
      <!-- 服务地点选择弹窗 -->
      <van-popup v-model="showLocationPicker" position="bottom" round class="location-popup">
        <div class="search-container">
          <van-search
            v-model="searchKeyword"
            placeholder="请输入服务地点名称"
            @input="onSearchInput"
          />
        </div>
        <van-cascader
          v-if="!isSearchMode"
          v-model="cascaderValue"
          title="选择服务地点"
          :options="locationOptions"
          @close="showLocationPicker = false"
          @finish="onLocationConfirm"
          active-color="#3562db"
        />
        <div v-else class="search-results">
          <div class="search-title">搜索结果</div>
          <div class="search-list">
            <div
              v-for="(item, index) in filteredLocations"
              :key="index"
              class="search-item"
              @click="selectSearchItem(item)"
            >
              {{ item.text }}
            </div>
            <div v-if="filteredLocations.length === 0" class="no-result">
              无匹配结果
            </div>
          </div>
        </div>
      </van-popup>
    </div>
    <div class="bottom-conten">
      <div class="situation">问题描述<span style="color: #ee0a24;">*</span></div>
      <van-field v-model="repairExplain" rows="2" autosize type="textarea" maxlength="1000"
                 placeholder="请输入问题描述，字数限制200字以内" :rules="[{ required: true, message: '请填写问题描述' }]" show-word-limit />
      <sounds-recording style="margin-top: 10px;" @getRecord="getRecord" @getRecordFile="getRecordFile"
                        @getInspRecord="getInspRecord" />
      <span style="color: #86909C;font-size: 14px;">注：语音最多录制60秒</span>
      <div class="situationUplod">
        <div class="imgTitle">上传附件</div>
        <div class="tolta">注：照片最多上传九张</div>
      </div>
      <div style="width: 100%;margin: 0 auto;">
        <van-uploader ref="uplodImg" v-model="fileList" :max-count="9" accept="image/*" :after-read="afterRead"
                      @delete="deleteImg" />
      </div>
    </div>
    <div class="bottom-conten">
      <div class="topItem">
        <van-field v-model="person" label="联系人" placeholder="请输入联系人">
        </van-field>
      </div>
      <div class="topItem notBoder">
        <van-field v-model="phoneNum" label="联系方式" placeholder="请输入联系方式">
        </van-field>
      </div>
    </div>
    <div class="bottom-bar">
      <van-button
        class="qualified"
        type="primary"
        :loading="isSubmitting"
        loading-text="提交中..."
        loading-type="spinner"
        @click="repairConfirm"
        block
        round>
        提交
      </van-button>
    </div>
  </div>
</template>
<script>
import { Toast, Popup } from "vant";
import UploadImage from "@/common/uploadImg/uploadImg";
import ImageCompressor from 'image-compressor.js'
import axios from 'axios'
import { mapState } from "vuex";
export default {
  components: {
    UploadImage
  },
  computed: {
    ...mapState(["serviceAreaTreeData"]),
    dynamicPadding() {
      return 'calc(1.08rem + 0.4rem)'
    }
  },
  watch:{
    radioCheck(val) {
      this.flagShow = val === '0' // 当选择"是"(0)时显示关联设备区块，选择"否"(1)时隐藏
      if(val==1){
        this.sourceDept = ''
        this.sourceDeptName = ''
        this.equipmentCode = ''
      }
    },
    searchVal(val) {
      // 修改过滤逻辑，同时匹配设备名称和编码
      this.searchOptions = this.sourcesDeptOptions.filter(item => {
        const searchText = val.toLowerCase();
        return item.assetName.toLowerCase().includes(searchText) ||
          item.assetCode.toLowerCase().includes(searchText);
      });
    },
  },
  data() {
    return {
      equipmentCode:"",
      paramsRegion:{},
      searchVal: "",
      searchOptions: [],
      sourcesDeptOptions: [],
      sourceDept: "",
      sourceDeptName: "",
      showPicker: false,
      radioCheck: '0',
      flagShow:true,
      repairExplain: '',
      files: [],
      fileList: [],
      person: '',
      phoneNum: '',
      recordingInfo: '',
      recordingInfoFile: '',
      pointInfo: {},
      attachmentUrl: [],
      isDevice: eval(this.$route.query.isDevice),
      inspVoice: '',
      loginInfo: {},
      staffInfo: {},
      inspSubImgStatus: false,
      oneSubImgStatus: false,
      filteredArray:[],
      locationName: '', // 服务地点名称(最后一级)
      locationId: '', // 服务地点ID(最后一级)
      locationFullPath: '', // 服务地点完整路径名称(用于显示)
      locationIdPath: '', // 服务地点完整ID路径(用于接口)
      locationNameArray: [], // 服务地点名称数组
      locationIdArray: [], // 服务地点ID数组
      inspectionPointType: '', // 点位类型
      needSelectLocation: false, // 是否需要手动选择服务地点
      showLocationPicker: false, // 服务地点选择器显示状态
      locationOptions: [], // 服务地点选择器数据
      cascaderValue: '', // 级联选择器当前值
      searchKeyword: '', // 搜索关键词
      isSearchMode: false, // 是否处于搜索模式
      filteredLocations: [], // 搜索过滤后的结果
      flatLocationList: [], // 扁平化的位置列表，用于搜索
      isSubmitting: false // 防抖状态，防止重复提交
    }
  },
  created() {
    console.log(this.$route.query,'this.$route.querythis.$route.query');
    console.log(JSON.parse(this.$route.query.answerMapList),'JSON.parseJSON.parseJSON.parseJSON.parseJSON.parse');
    //  const idArray = this.$route.query.bookArr.flatMap(item => item.maintainProjectdetailsTermReleaseList?item.maintainProjectdetailsTermReleaseList.map(subItem => subItem.id):'');
    //   console.log(idArray,'idArrayidArrayidArray');
    //  this.filteredArray = JSON.parse(this.$route.query.answerMapList).filter(item => idArray.includes(item.id));


    this.loginInfo = JSON.parse(localStorage.getItem("loginInfo"))
    this.staffInfo = JSON.parse(localStorage.getItem("staffInfo"))
    this.pointInfo = this.$route.query.pointRelease
    this.person = this.loginInfo.staffName
    this.phoneNum = this.loginInfo.phone

    // 初始化服务地点信息
    const particulars = JSON.parse(this.pointInfo.particulars)
    console.log(particulars,'particularsparticulars1111');
    this.inspectionPointType = particulars.inspectionPointType || particulars.taskPointTypeCode

    // 如果是自定义点位(inspectionPointType为3)或者没有返回服务地点，则需要手动选择
    if (this.inspectionPointType === '3' || (!particulars.simName && !particulars.regionName)) {
      this.needSelectLocation = true
      this.initLocationColumns()
    } else if (this.inspectionPointType === '1') {
      // 从详情接口获取的服务地点信息
      this.locationName = particulars.simName
      this.locationId = particulars.ssmId

      // 获取完整的路径信息
      this.getFullLocationPath(particulars.ssmId)
    } else {
      // 从详情接口获取的服务地点信息
      this.locationName = particulars.regionName
      this.locationId = particulars.regionCode

      // 获取完整的路径信息
      this.getFullLocationPath(particulars.regionCode)
    }

    // 不再自动填充问题描述，需要用户手动填写
    this.repairExplain = ''
  },
  mounted() {
    this.getAllOffice()
    this.$YBS.apiCloudEventKeyBack(this.goBack);
  },
  methods: {
    //点击选择设备
    clickEquipment() {
      if(!this.cascaderValue || !this.locationFullPath) {
        Toast('请先选择服务地点');
        return
      }else{
        this.showPicker = true
      }
    },
    getAllOffice() {
      if(this.inspectionPointType==1){
        this.paramsRegion = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          currentPage:1,
          pageSize:99999,
          regionCode:JSON.parse(this.pointInfo.particulars).id
        };

      }else if(this.inspectionPointType==3){
        this.paramsRegion = {
          unitCode: this.loginInfo.unitCode,
          hospitalCode: this.loginInfo.hospitalCode,
          currentPage:1,
          pageSize:99999,
          regionCode:this.cascaderValue
        };
      }else if(this.inspectionPointType==2){
        return
      }
      this.$api.deviceDetailsCopy(this.paramsRegion).then(res => {
        console.log(res,'resresresresres');
        if(!this.cascaderValue) {
          this.sourcesDeptOptions = [];
          this.searchOptions = [];
        } else {
          // 添加displayText字段用于显示
          this.sourcesDeptOptions = res.assetDetailsList.map(item => ({
            ...item,
            displayText: `${item.assetName} ${item.assetCode}` // 组合显示文本
          }));
          this.searchOptions = this.sourcesDeptOptions;
        }

      });
    },
    onConfirm(value) {
      console.log(value,4544545);
      // this.sourceDept = value.value + "_" + value.label;
      this.sourceDept = value.assetsId;
      this.sourceDeptName = value.assetName;
      this.equipmentCode = value.assetCode
      this.showPicker = false;
    },
    goBack() {
      this.$router.go(-1)
    },
    async afterRead(files) {
      this.fileList.forEach(i => {
        return (i.status = "uploading");
      });
      // 上传至一站式
      const params = new FormData();
      const subFile = await this.compressImage(files)
      params.append('file', subFile)
      this.subImg(params);
      // 上传至巡检
      let formData = new FormData()
      formData.append("hospitalCode", this.loginInfo.hospitalCode)
      formData.append("unitCode", this.loginInfo.unitCode)
      formData.append('file', subFile)
      this.subInsp(formData)
    },
    // 图片压缩
    compressImage(file) {
      return new Promise((resolve, reject) => {
        new ImageCompressor(file.file, {
          quality: 0.6,
          checkOrientation: false,
          success(res) {
            let file = new window.File([res], res.name, { type: res.type })
            resolve(file)
          },
          error(e) {
            reject();
          }
        })
      })
    },
    // 一站式图片上传
    subImg(params) {
      axios({
        method: 'post',
        url: __PATH.ONESTOP + '/minio/upload',
        data: params,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          'Authorization': localStorage.getItem("token")
        }
      }).then(res => {
        if ((res.data.code = "200")) {
          this.fileList[this.fileList.length - 1].url = res.data.data.picUrl
          this.fileList.forEach(i => {
            return (i.status = "done")
          })
          this.oneSubImgStatus = true
        }
      })
    },
    // 巡检图片上传
    subInsp(formData) {
      axios({
        method: 'post',
        url: __PATH.IPSM_URL + '/file/upload',
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          'Authorization': localStorage.getItem("token")
        }
      }).then((res) => {
        if (res.data.code == 200) {
          this.fileList.forEach(i => {
            return i.status = 'done'
          })
          this.attachmentUrl.push(res.data.data.fileKey)
          this.fileList.forEach(i => {
            return i.status = 'done'
          })
          this.inspSubImgStatus = true
        }
      }).catch(() => {
        this.fileList.forEach(i => {
          return i.status = 'failed'
        })
        this.$toast.fail('上传失败')
      })
    },
    deleteImg() {
      this.fileList = this.fileList.filter(i => i.name != e.file.name)
    },
    getRecord(info) {
      this.recordingInfo = info;
    },
    getRecordFile(info) {
      this.recordingInfoFile = info;
    },
    getInspRecord(info) {
      this.inspVoice = info
    },
    // 初始化服务地点选择器数据
    initLocationColumns() {
      if (this.serviceAreaTreeData && this.serviceAreaTreeData.length > 0) {
        // 深拷贝数据，避免修改原始数据
        const serviceAreaTreeDataCopy = JSON.parse(JSON.stringify(this.serviceAreaTreeData))
        // 将列表数据转换为树形结构
        const treeData = this.$YBS.transData(serviceAreaTreeDataCopy, "id", "parentId", "children")
        this.locationOptions = this.formatLocationData(treeData)
        // 生成扁平化的位置列表，用于搜索
        this.generateFlatLocationList()
      }
      // 如果没有数据，就不做处理
    },

    // 生成扁平化的位置列表，用于搜索
    generateFlatLocationList() {
      this.flatLocationList = []
      this.flattenLocationOptions(this.locationOptions)
    },

    // 递归扁平化位置选项
    flattenLocationOptions(options, parentPath = '', parentValues = []) {
      options.forEach(option => {
        const currentPath = parentPath ? `${parentPath}>${option.text}` : option.text
        const currentValues = [...parentValues, option.value]

        // 添加到扁平列表
        this.flatLocationList.push({
          text: currentPath,
          value: option.value,
          rawText: option.text,
          // 存储完整的值路径，用于级联选择器回显
          valuePath: currentValues
        })

        // 递归处理子选项
        if (option.children && option.children.length > 0) {
          this.flattenLocationOptions(option.children, currentPath, currentValues)
        }
      })
    },

    // 格式化服务地点数据为级联选择器格式
    formatLocationData(data) {
      const result = []
      if (!data || !data.length) return result

      data.forEach(item => {
        const obj = {
          text: item.ssmName || item.name,
          value: item.id
        }

        // 只有当children存在且长度大于0时，才添加children属性
        if (item.children && item.children.length > 0) {
          obj.children = this.formatLocationData(item.children)
        }

        result.push(obj)
      })
      return result
    },

    // 服务地点选择确认
    onLocationConfirm({ selectedOptions, value }) {
      // selectedOptions是一个数组，包含用户选择的每一级选项
      if (selectedOptions && selectedOptions.length > 0) {
        // 获取最后一级的名称和ID
        const lastItem = selectedOptions[selectedOptions.length - 1]
        this.locationName = lastItem.text
        this.locationId = lastItem.value

        // 保存所有层级的名称和ID
        this.locationNameArray = selectedOptions.map(option => option.text)
        this.locationIdArray = selectedOptions.map(option => option.value)

        // 设置完整路径
        this.locationFullPath = this.locationNameArray.join('>')
        this.locationIdPath = this.locationIdArray.join(',')

        // 保存选中的值，用于回显
        this.cascaderValue = value
        console.log(this.cascaderValue,'选择的服务地点11111');
        console.log(selectedOptions,'选择的服务地点222222');
        this.getAllOffice()
      }
      this.resetSearch()
      this.showLocationPicker = false
    },

    // 搜索输入处理
    onSearchInput() {
      if (this.searchKeyword) {
        this.isSearchMode = true
        this.filterLocations()
      } else {
        this.isSearchMode = false
        this.filteredLocations = []
      }
    },

    // 过滤位置列表
    filterLocations() {
      if (!this.searchKeyword) {
        this.filteredLocations = []
        return
      }

      const keyword = this.searchKeyword.toLowerCase()
      this.filteredLocations = this.flatLocationList.filter(item =>
        item.text.toLowerCase().includes(keyword)
      )
    },

    // 选择搜索结果项
    selectSearchItem(item) {
      // 设置最后一级名称和ID
      this.locationName = item.rawText
      this.locationId = item.value

      // 解析完整路径
      const pathParts = item.text.split('>')
      this.locationNameArray = pathParts
      this.locationFullPath = item.text

      // 查找完整的ID路径
      if (item.valuePath && item.valuePath.length > 0) {
        // 直接使用valuePath作为ID路径
        this.locationIdArray = [...item.valuePath]
        this.locationIdPath = item.valuePath.join(',')

        // 设置级联选择器的值，以便下次打开时能够回显
        this.cascaderValue = item.valuePath[item.valuePath.length - 1]
      } else {
        // 兜底处理
        this.locationIdArray = [item.value]
        this.locationIdPath = item.value
        this.cascaderValue = item.value
      }

      this.resetSearch()
      this.getAllOffice()
      this.showLocationPicker = false
    },

    // 重置搜索
    resetSearch() {
      this.searchKeyword = ''
      this.isSearchMode = false
      this.filteredLocations = []
    },

    // 获取服务地点的完整路径信息
    getFullLocationPath(locationId) {
      // 设置基本值
      this.locationFullPath = this.locationName
      this.locationIdPath = locationId
      this.locationNameArray = [this.locationName]
      this.locationIdArray = [locationId]

      // 只有当服务地点数据已存在时才尝试查找完整路径
      if (this.serviceAreaTreeData && this.serviceAreaTreeData.length > 0) {
        // 使用深拷贝后的数据进行处理
        this.findLocationPathById(locationId)
      }
      // 如果没有数据，就使用已设置的基本值
    },

    // 根据ID查找服务地点的完整路径
    findLocationPathById(locationId) {
      try {
        // 先生成扁平化的位置列表，确保数据已处理
        if (this.flatLocationList.length === 0) {
          // 深拷贝数据，避免修改原始数据
          const serviceAreaTreeDataCopy = JSON.parse(JSON.stringify(this.serviceAreaTreeData))
          const treeData = this.$YBS.transData(serviceAreaTreeDataCopy, "id", "parentId", "children")
          this.locationOptions = this.formatLocationData(treeData)
          this.generateFlatLocationList()
        }

        // 在扁平列表中查找对应的位置信息
        const foundLocation = this.flatLocationList.find(item => item.value === locationId)
        if (foundLocation) {
          // 找到了完整路径信息
          this.locationFullPath = foundLocation.text
          this.locationNameArray = foundLocation.text.split('>')

          if (foundLocation.valuePath && foundLocation.valuePath.length > 0) {
            this.locationIdArray = [...foundLocation.valuePath]
            this.locationIdPath = foundLocation.valuePath.join(',')

            // 设置级联选择器的值，以便下次打开时能够回显
            this.cascaderValue = foundLocation.valuePath[foundLocation.valuePath.length - 1]
          }
        }
      } catch (error) {
        // 如果处理过程中出错，保持使用基本值
        console.log('查找服务地点路径失败，使用基本值')
      }
    },

    // 提交
    repairConfirm() {
      // van-button的loading属性会自动防止重复点击
      if (this.isSubmitting) {
        return;
      }

      if (!this.person) {
        return this.$toast.fail("请输入联系人");
      }
      if (!this.phoneNum) {
        return this.$toast.fail("请输入联系方式");
      }
      if (this.needSelectLocation && !this.locationName) {
        return this.$toast.fail("请选择服务地点");
      }
      if((this.inspectionPointType ==1 || this.inspectionPointType ==3)&& !this.sourceDeptName && this.radioCheck=='0' ){
        return this.$toast.fail("请选择设备");
      }
      if (!this.repairExplain) {
        return this.$toast.fail("请填写问题描述");
      }
      if (this.fileList.length && (!this.inspSubImgStatus || !this.oneSubImgStatus)) {
        return this.$toast.fail("请等待图片上传完成");
      }

      // 设置loading状态
      this.isSubmitting = true;
      const urls = this.fileList.length
        ? this.fileList.map((item) => {
          console.log('提交的url', item.url)
          return item.url;
        })
        : [];
      const params = {
        typeSources:'2',
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        sysForShort: 'icms',
        createByJob: '',
        jobNumber: this.staffInfo.staffNumber || '', // 工号
        type: '1',
        questionDescription: this.repairExplain, // 描述
        taskPointName: this.pointInfo.taskPointName, // 巡检点名称
        taskPointId: this.pointInfo.taskPointId, // 巡检点id
        localtionName: this.inspectionPointType == 1
          ? `${this.locationFullPath || this.locationName}>${JSON.parse(this.pointInfo.particulars).ssmName}`
          : this.locationFullPath || this.locationName, // 服务地点完整路径名称
        localtion: this.inspectionPointType == 1
          ? `${this.locationIdPath || this.locationId},${JSON.parse(this.pointInfo.particulars).id}`
          : this.locationIdPath || this.locationId, // 服务地点完整ID路径
        callerCode: this.loginInfo.id, // 报修人id
        callerName: this.person, // 报修人name
        sourcesPhone: this.phoneNum, // 报修人电话
        workTypeCode: this.$route.query.isRepairSelf == 1 ? '8' : '10',
        attachment: JSON.stringify(urls), // 附件
        callerTape: this.recordingInfo, // 语音
        phoneNumber: this.phoneNum, // 报修人电话
        realName: this.person, // 报修人name,
        deptCode: this.staffInfo.teamId, // 报修人科室id
        deptName: this.staffInfo.teamName, // 报修人科室name
        sourcesDept: this.staffInfo.officeId,
        sourcesDeptName: this.staffInfo.officeName,
        userId: this.loginInfo.id,
        staffId: this.loginInfo.staffId,
        workSources: '1',
        isRepairSelf:this.$route.query.isRepairSelf,
        excuteStartTime:this.$route.query.entryTime,
        deviceId:this.inspectionPointType==2?JSON.parse(this.pointInfo.particulars).assetsId:this.sourceDept,
        deviceNumber:this.inspectionPointType==2?JSON.parse(this.pointInfo.particulars).assetCode:this.equipmentCode,
        deviceName:this.inspectionPointType==2?JSON.parse(this.pointInfo.particulars).assetName:this.sourceDeptName
      }
      if(this.$route.query.isRepairSelf == 1) {
        params.designateDeptCode = this.staffInfo.teamId ? this.staffInfo.teamId.split(',')[0] : '';
        params.designateDeptName = this.staffInfo.teamName ? this.staffInfo.teamName.split(',')[0] : '';
        params.designatePersonCode = this.staffInfo.teamPersonId;
        params.designatePersonName = this.staffInfo.staffName;
        params.designatePersonPhone = this.loginInfo.phone;
      }
      Toast.loading({
        message: "正在提交...",
        forbidClick: true,
        duration: 0
      });
      this.$api.taskRepair(params).then(res => {
        Toast.clear()
        if (res.workNum) {
          this.submitToIcms(res, params);
        } else {
          this.isSubmitting = false; // 重置提交状态
          this.$toast.error("报修失败");
        }
      }).catch(error => {
        Toast.clear()
        this.isSubmitting = false; // 重置提交状态
        this.$toast.error("报修失败");
      })
    },
    //反馈信息提交到icms
    submitToIcms(data, params) {
      if (!this.pointInfo) return;
      let param = {};
      let arr = [];
      if (this.pointInfo.maintainProjectRelease.maintainProjectdetailsReleaseList.length > 0) {
        this.pointInfo.maintainProjectRelease.maintainProjectdetailsReleaseList.forEach(
          (item) => {
            if (item.maintainProjectdetailsTermReleaseList && item.maintainProjectdetailsTermReleaseList.length > 0) {
              item.maintainProjectdetailsTermReleaseList.forEach((item2) => {
                if (item2.isNum == "3") {
                  let obj = {
                    id: item2.id,
                    value: item2.radio,
                  };
                  arr.push(obj);
                } else if (item2.isNum != "1") {
                  let obj = {
                    id: item2.id,
                    value: item2.value,
                  };
                  arr.push(obj);
                }
              });
            }
          }
        )
      }

      param.unitCode = this.loginInfo.unitCode
      param.hospitalCode = this.loginInfo.hospitalCode
      param.answerMapList = this.$route.query.answerMapList;
      param.excuteStartTime =  this.$route.query.entryTime;
      param.timeoutDeclaration =  this.$route.query.timeoutDeclaration;
      param.attachmentUrl = this.attachmentUrl.join(','),
        param.callerTapeUrl = this.inspVoice // 语音
      param.state = "4";
      param.isBookEmpty = false;
      param.guaranteeCode = data.workNum;
      param.taskId = this.pointInfo.taskId;
      param.taskPointReleaseId = this.pointInfo.maintainProjectRelease.taskPointReleaseId;
      param.staffId = this.loginInfo.staffId;
      param.staffName = this.loginInfo.staffName;
      param.details = params.questionDescription;
      param.spyScan = sessionStorage.getItem('whetherLocation') || 1, // 定位状态
        this.$api.inspectionSubmit(param).then(res => {
          this.saveRecod()
        }).catch(error => {
          Toast.clear()
          this.isSubmitting = false; // 重置提交状态
          this.$toast.error("提交到ICMS失败");
        })
    },
    // 保存操作记录
    saveRecod() {
      let taskPointTypeCode = JSON.parse(this.pointInfo.particulars).taskPointTypeCode
      let ssmId = JSON.parse(this.pointInfo.particulars).ssmId
      let zdyId = JSON.parse(this.pointInfo.particulars).id
      const record = {
        unitCode: this.loginInfo.unitCode,
        hospitalCode: this.loginInfo.hospitalCode,
        assetsId: this.isDevice ? this.isDevice : (taskPointTypeCode === 'zdy' ? zdyId : ssmId),
        operationId: this.pointInfo.maintainProjectRelease.taskPointReleaseId,
        operationCode: 3, // 1:巡检 2:保养 3:报修
        operation: '报修',
        record: `报修单号：` + this.pointInfo.maintainProjectRelease.taskPointReleaseId,
      }
      this.$api.saveOperateRecord(record).then(res => {
        console.log(res)
        Toast.clear()
        this.$toast.success("报修成功");
        setTimeout(() => {
          this.$router.go(-2)
        }, 1000)
      }).catch(error => {
        Toast.clear()
        this.isSubmitting = false; // 重置提交状态
        this.$toast.error("操作记录保存失败");
      })
    }
  }
}
</script>
<style scoped lang="stylus">
.inner
  min-height 100vh
  font-size 0.32rem
  background-color #F2F4F9
  .contenTop
    padding 0 0.32rem
    background-color #fff
    .topItem
      height 1.08rem
      display flex
      align-items center
      border-bottom 1px solid #E5E6EB
      .itemTitle
        width 2rem
        color #4E5969
        flex-shrink 0
      .itemConten
        color #1D2129
        word-break break-all
        flex 1
        display flex
        align-items center
  .bottom-conten
    border-top 0.2rem solid #F2F4F9
    padding 0 0.32rem
    background-color #fff
    .situation
      height 1.08rem
      line-height 1.08rem
      color #1D2129
    .situationUplod
      height 1.08rem
      display flex
      align-items center
      justify-content space-between
      border-top 1px solid #e5e6eb
      .imgTitle
        color #1D2129
      .tolta
        font-size 14px
        color #86909C
    .topItem
      height 1.08rem
      display flex
      align-items center
      border-bottom 1px solid #E5E6EB
      .itemTitle
        width 2rem
        color #4E5969
      .itemConten
        color #1D2129
    .notBoder
      border none
  .bottom-bar
    width calc(100% - 0.64rem)
    height 1.08rem
    background-color #fff
    position fixed
    bottom 0
    padding 0.2rem 0.32rem 0 0.32rem
    display flex
    justify-content space-between
    .qualified
      width 100%
      height 0.88rem
      background-color #3562DB
      border-color #3562DB
      border-radius 0.04rem
  /deep/.van-cell
    padding 0.2rem 0

.location-popup
  height 70%
  .search-container
    padding 10px
  .search-results
    height calc(100% - 54px)
    overflow-y auto
    padding 0 15px
    .search-title
      font-size 16px
      font-weight bold
      padding 10px 0
    .search-list
      .search-item
        padding 12px 0
        border-bottom 1px solid #ebedf0
        &:active
          background-color #f2f3f5
      .no-result
        padding 20px 0
        text-align center
        color #969799
</style>
